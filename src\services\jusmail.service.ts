import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import logger from '../utils/logger';
import { ConfigError, ExternalServiceError, ValidationError, NotFoundError } from '../types/errors';
import { ZohoDomainService } from './zoho-domain.service';
import { OpenAIService } from './openai.service';
import { DomainService } from './domain.service';
import { Route53Service } from './route53.service';
import { JoinvixService } from './joinvix.service';
import { JusMailConfigurationRepository } from '../repositories/jusmail-configuration.repository';
import { JusMailConfigurationStatus } from '../models/JusMailConfiguration';

// Environment variables
const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID || '';
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET || '';
const ZOHO_ORGANIZATION_ID = process.env.ZOHO_ORGANIZATION_ID || '';
const ZOHO_SCOPES = process.env.ZOHO_SCOPES || 'ZohoMail.organization.domains.ALL';

/**
 * Service for managing Zoho domains
 * Provides functionality to add, fetch, and verify domains in a Zoho organization
 */
export class JusMailService {
    private domainService: DomainService;
    private openaiService: OpenAIService;
    private route53Service: Route53Service;
    private jusmailConfigRepository: JusMailConfigurationRepository;
    private joinvixService: JoinvixService;
    private zohoDomainService: ZohoDomainService;

    constructor() {
        this.domainService = new DomainService();
        this.openaiService = new OpenAIService();
        this.route53Service = new Route53Service();
        this.jusmailConfigRepository = new JusMailConfigurationRepository();
        this.joinvixService = new JoinvixService();
        this.zohoDomainService = new ZohoDomainService();
    }

    async searchDomain(domainName: string): Promise<any> {
        try {
            let domainAvailable = false;

            try {
                await this.domainService.searchDomain(domainName);
            } catch (error) {
                if (error instanceof NotFoundError) {
                domainAvailable = true;
                } else {
                logger.error('Error checking domain availability', {
                    error: (error as Error).message,
                    stack: (error as Error).stack,
                    domainName
                }, 'jusmail-service/search-domain');
                throw error;
                }
            }

            logger.info('Domain availability check completed', {
                domainName,
                available: domainAvailable
            }, 'jusmail-service/search-domain');

            return domainAvailable;
        } catch (error) {
            logger.error('Error checking domain availability', {
                error: (error as Error).message,
                stack: (error as Error).stack,
                domainName
            }, 'jusmail-service/search-domain');

            if (axios.isAxiosError(error) && error.response) {
                logger.error('JoinVix API error response', {
                    status: error.response.status,
                    data: error.response.data
                }, 'jusmail-service/search-domain');

                throw new ExternalServiceError(
                `Error checking domain availability: ${error.response.data?.message || error.message}`,
                'jusmail-api'
                );
            }

            throw new ExternalServiceError(`Error checking domain availability: ${(error as Error).message}`, 'jusmail-api');
        }
    }

    async generateDomainSuggestions(name: string): Promise<string[]> {
        try {
            if (!name || name.trim().length === 0) {
                throw new ValidationError('Name is required for domain generation');
            }

            const suggestions = await this.openaiService.generateDomainSuggestions(name);

            const results = [];
            
            for (let suggestion of suggestions) {
                try {
                    await axios.get(`${process.env.REGISTRO_BR_SEARCH}${suggestion}`);
                } catch (error) {
                    if (axios.isAxiosError(error) && error.response) {
                        if (error.response.status === 404) {
                            results.push(suggestion);
                        }
                    }
                }
            }

            logger.info('Successfully generated domain suggestions', {
                name: name.trim(),
                suggestionsCount: suggestions.length
            }, 'jusmail-service/generate-domain-suggestions');

            return results;
            } catch (error) {
            logger.error('Error generating domain suggestions', {
                error: (error as Error).message,
                stack: (error as Error).stack,
                name: name?.trim()
            }, 'jusmail-service/generate-domain-suggestions');

            if (error instanceof ValidationError || error instanceof ExternalServiceError) {
                throw error;
            }

            throw new ExternalServiceError(`Error generating domain suggestions: ${(error as Error).message}`, 'jusmail-api');
        }
    }

  /**
   * Create a domain order in JoinVix
   * @param params Domain order parameters
   * @returns Order creation response
   */
  async createOrder(user_id: number, client_id: number, domain: string, email: string): Promise<any> {
    try {
      logger.info('Creating domain order', { domain, email }, 'jusmail-service/create-order');

      const available = await this.searchDomain(domain);
      if (!available) {
        throw new ValidationError('Domain is not available');
      }

      const domains = await this.route53Service.listRoute53Domains();
      const subId = domains.find(d => d.name === domain)?.id;

      if (subId) {
        throw new ValidationError(`Domain name is not available`);
      }

      const awsCreateHZ = await this.route53Service.createHostedZone(domain);
      if (!awsCreateHZ) {
        throw new ValidationError('Error creating hosted zone');
      }

      const hostedZoneId = 'Z06507317BTHBYU79AGF' // awsCreateHZ?.hostedZoneId; REMOVER JUSFINDER
      const nameservers = awsCreateHZ?.nameservers;
      const [ns1, ns2, ns3, ns4] = nameservers || [];

      const newConfigId = await this.jusmailConfigRepository.create({
        user_id,
        client_id,
        domain_name: 'jusfinder.com.br', // REMOVER JUSFINDER
        email,
        hosted_zone_id: hostedZoneId
      });

      if (!newConfigId) {
        throw new ValidationError('Error creating config');
      }

      const response = await this.joinvixService.createAndAcceptOrder({
        domain,
        ns1,
        ns2,
        ns3,
        ns4
      });

      if (response.data?.result !== 'success') {
        throw new ExternalServiceError('Error creating domain order', 'joinvix-api');
      }

      const updatedStatusAccepted = await this.jusmailConfigRepository.update(newConfigId, { status: JusMailConfigurationStatus.ACCEPTED });
      if (!updatedStatusAccepted) {
          throw new ValidationError('Error updating config');
      }

      logger.info('Domain order created and accepted successfully', {
        domain: domain,
        response: response.data
      }, 'jusmail-service/create-order');

      const responseZoho = await this.zohoDomainService.addDomainToOrganization('jusfinder.com.br', email); // REMOVER JUSFINDER
      if (!responseZoho) {
        throw new ValidationError('Error adding domain to Zoho');
      }

      return responseZoho.data;
    } catch (error) {
      logger.error('Error creating domain order', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain: domain
      }, 'jusmail-service/create-order');

      if (axios.isAxiosError(error) && error.response) {
        logger.error('JoinVix API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'jusmail-service/create-order');

        throw new ExternalServiceError(
          `Error creating domain order: ${error.response.data?.message || error.message}`,
          'joinvix-api'
        );
      }

      throw new ExternalServiceError(`Error creating domain order: ${(error as Error).message}`, 'joinvix-api');
    }
  }


}
