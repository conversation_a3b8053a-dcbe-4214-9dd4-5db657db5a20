import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import logger from '../utils/logger';
import { ConfigError, ExternalServiceError, ValidationError, NotFoundError } from '../types/errors';
import { ZohoAuthService } from './zoho-auth.service';
import { Route53Service } from './route53.service';
import { JusMailConfigurationRepository } from '../repositories/jusmail-configuration.repository';
import { JusMailConfigurationStatus } from '../models/JusMailConfiguration';

// Environment variables
const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID || '';
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET || '';
const ZOHO_ORGANIZATION_ID = process.env.ZOHO_ORGANIZATION_ID || ''; // REMOVER JUSFINDER
const ZOHO_SCOPES = process.env.ZOHO_SCOPES || 'ZohoMail.organization.domains.ALL';

/**
 * Service for managing Zoho domains
 * Provides functionality to add, fetch, and verify domains in a Zoho organization
 */
export class ZohoDomainService {
  private zohoAuthService: ZohoAuthService;
  private route53Service: Route53Service;
  private jusmailConfigRepository: JusMailConfigurationRepository;
  private baseUrl = 'https://mail.zoho.com/api';

  constructor() {
    // Validate required environment variables
    if (!ZOHO_CLIENT_ID || !ZOHO_CLIENT_SECRET) {
      logger.error('Zoho OAuth credentials or refresh token are not set', {}, 'zoho-domain/constructor');
      throw new ConfigError('Zoho OAuth credentials or refresh token are not set');
    }

    if (!ZOHO_ORGANIZATION_ID) {
      logger.error('Zoho Organization ID is not set', {}, 'zoho-domain/constructor');
      throw new ConfigError('Zoho Organization ID is not set');
    }

    if (!ZOHO_SCOPES) {
      logger.warn('Zoho scopes not set, using default scope', {}, 'zoho-domain/constructor');
    }

    this.zohoAuthService = new ZohoAuthService();
    this.route53Service = new Route53Service();
    this.jusmailConfigRepository = new JusMailConfigurationRepository();
  }

  /**
   * Get a fresh access token using the refresh token
   * @returns Access token for API requests
   */
  private async getAccessToken(): Promise<string> {
    try {
      logger.debug('Getting fresh access token using refresh token', { scopes: ZOHO_SCOPES }, 'zoho-domain/get-access-token');

      const tokenData = await this.zohoAuthService.refreshAccessToken();

      logger.debug('Successfully obtained fresh access token', {}, 'zoho-domain/get-access-token');
      return tokenData.access_token;
    } catch (error) {
      logger.error('Error getting access token', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        scopes: ZOHO_SCOPES
      }, 'zoho-domain/get-access-token');

      throw new ExternalServiceError(`Error getting Zoho access token: ${(error as Error).message}`, 'zoho-api');
    }
  }

  /**
   * Add a domain to a Zoho organization
   * @param domainName Domain name to add (e.g., 'example.com')
   * @returns Domain details including verification codes
   */
  async addDomainToOrganization(domain: string, email: string): Promise<any> {
    const domainName = domain.toLowerCase();
    try {

      logger.info('Adding domain to organization', { domainName }, 'zoho-domain/add-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Get fresh access token
      const accessToken = await this.getAccessToken();

      const zohoConfig = await this.jusmailConfigRepository.getByDomainName(domainName);

      /* FIXO NO MOMENTO */
      const hostedZoneId = 'Z06507317BTHBYU79AGF'; // zohoConfig?.hosted_zone_id; REMOVER_JUSFINDER

      if (!hostedZoneId) {
        throw new ValidationError('Hosted zone ID not found');
      }

      // Make API request to add domain
      const response = await axios.post(
        `${this.baseUrl}/organization/${ZOHO_ORGANIZATION_ID}/domains`,
        { domainName },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Zoho-oauthtoken ${accessToken}`
          }
        }
      );
      console.log("🚀 ~ ZohoDomainService ~ addDomainToOrganization ~ response:", response)

      const updatedStatusRegistered = await this.jusmailConfigRepository.update(zohoConfig.id, { zoho_domain_data: response.data, status: JusMailConfigurationStatus.REGISTERED });
      if (!updatedStatusRegistered) {
        throw new ValidationError('Error updating config');
      }

      logger.info('Domain added successfully', { domainName }, 'zoho-domain/add-domain');

      const CNAMEVerificationCode = response.data.data.CNAMEVerificationCode;
      logger.info('Got CNAME verification code', {
        domainName,
        CNAMEVerificationCode
      }, 'zoho-domain/add-domain');

      const resultCNAME = await this.createAndVerifyCNAME(hostedZoneId, domainName, ZOHO_ORGANIZATION_ID, CNAMEVerificationCode, zohoConfig.id);

      if (!resultCNAME) {
        throw new ValidationError('Error creating and verifying CNAME');
      }

      logger.info('CNAME created and verified', {
        domainName,
        result: resultCNAME?.status?.code
      }, 'zoho-domain/add-domain');

      await this.enableDisableEmailHosting(ZOHO_ORGANIZATION_ID, domainName, true);

      const dkimResult = await this.addDKIMDetails(ZOHO_ORGANIZATION_ID, domainName);
      logger.debug('DKIM details added result', {
        domainName,
        result: dkimResult?.status?.code
      }, 'zoho-domain/add-domain');

      const updatedDKIM = await this.jusmailConfigRepository.update(zohoConfig.id, {
        dkim_id: dkimResult.data.dkimId,
        domain_id: dkimResult.data.domainId,
        dkim_public_key: dkimResult.data.publicKey
      });
      if (!updatedDKIM) {
        throw new ValidationError('Error updating config');
      }

      const promiseDKIM = this.createAndVerifyDKIM(hostedZoneId, `zmail._domainkey.${domainName}`, dkimResult.data.publicKey, domainName, ZOHO_ORGANIZATION_ID, dkimResult.data.dkimId);
      const promiseSPF = this.createAndVerifySPF(hostedZoneId, domainName, ZOHO_ORGANIZATION_ID);
      const promiseMX = this.createAndVerifyMX(hostedZoneId, domainName, ZOHO_ORGANIZATION_ID);

      const results = await Promise.all([promiseDKIM, promiseSPF, promiseMX]);

      // Aqui tem que adicionar a conta e gravar o account id na tabela

      return response.data;
    } catch (error) {
      logger.error('Error adding domain to organization', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/add-domain');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-domain/add-domain');

        throw new ExternalServiceError(
          `Error adding domain to Zoho organization: ${error.response.data?.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error adding domain to Zoho organization: ${(error as Error).message}`, 'zoho-api');
    }
  }

  async createAndVerifyCNAME(
    hostedZoneId: string,
    domainName: string,
    organization_id: string,
    CNAMEVerificationCode: string,
    config_id: string
  ): Promise<any> {

    logger.info('Creating and verifying CNAME', { hostedZoneId, domainName }, 'zoho-domain/create-and-verify-cname');

    // Create the CNAME validation record in Route53
    const response = await this.route53Service.createSubdomain({
      hostedZoneId: hostedZoneId,
      subdominio: `${CNAMEVerificationCode}.${domainName}`,
      destino: 'zmverify.zoho.com',
      tipo: 'CNAME',
      ttl: 300
    });

    if (!response) {
      throw new ValidationError('Error creating SPF record');
    }

    const updatedCNAME = await this.jusmailConfigRepository.update(config_id, { verification_code: CNAMEVerificationCode, status: JusMailConfigurationStatus.VERIFICATION_PENDING });
    if (!updatedCNAME) {
      throw new ValidationError('Error updating config');
    }

    const verifyResult = await this.verifyDomain(domainName, config_id, 'CNAME', );
    logger.debug('Domain verification result', {
      domainName,
      result: verifyResult?.status?.code
    }, 'zoho-domain/add-domain');

    return verifyResult;
  }

  async createAndVerifyDKIM(
    hostedZoneId: string,
    subdominio: string,
    publicKey: string,
    domainName: string,
    organization_id: string,
    dkim_id: string
  ): Promise<any> {

    logger.info('Verifying DKIM with retry mechanism', { hostedZoneId, subdominio }, 'zoho-domain/verify-domain');

    const response = await this.route53Service.createSubdomain({
      hostedZoneId: hostedZoneId,
      subdominio: subdominio,
      destino: `"${publicKey}"`,
      tipo: 'TXT',
      ttl: 300
    });

    if (!response) {
      throw new ValidationError('Error creating DKIM record');
    }

    let lastError: any = null;
    let retryCount = 0;
    const maxRetries = 20;
    const delayMs = 1000;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          logger.info(`Retry attempt ${retryCount}/${maxRetries} for domain verification`,
            { hostedZoneId, subdominio },
            'zoho-domain/verify-domain');
          // Wait before retrying
          await this.delay(delayMs);
        } else {
          logger.info('Verifying domain', { hostedZoneId, subdominio }, 'zoho-domain/verify-domain');
        }

        // Get fresh access token
        const accessToken = await this.getAccessToken();

        // Make API request to verify domain
        const response = await axios.put(
          `${this.baseUrl}/organization/${organization_id}/domains/${domainName}`,
          {
            mode: 'verifyDkimKey',
            dkimId: dkim_id
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Zoho-oauthtoken ${accessToken}`
            }
          }
        );

        // Check if verification was successful
        if (response.data?.status?.code === 200 && response.data?.data?.dkimstatus === true) {
          logger.info('Domain verified successfully', { domainName, retryAttempt: retryCount }, 'zoho-domain/verify-domain');
          // Return successful response
          return response.data;
        } else {
          logger.warn('Domain verification failed', {
            domainName,
            retryAttempt: retryCount,
            error: response.data?.data?.error,
            message: response.data?.data?.message
          }, 'zoho-domain/verify-domain');

          // Store the error for potential retry
          lastError = new ExternalServiceError(
            `Domain verification failed: ${response.data?.data?.error || response.data?.data?.message || 'Unknown error'}`,
            'zoho-api'
          );

          // If we haven't reached max retries, continue to next attempt
          if (retryCount < maxRetries) {
            retryCount++;
            continue;
          }

          // Return the response even if verification failed but API call succeeded
          return response.data;
        }
      } catch (error) {
        logger.error('Error verifying domain', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          domainName,
          retryAttempt: retryCount
        }, 'zoho-domain/verify-domain');

        // Store the error for potential retry
        if (axios.isAxiosError(error) && error.response) {
          logger.error('Zoho API error response', {
            status: error.response.status,
            data: error.response.data,
            retryAttempt: retryCount
          }, 'zoho-domain/verify-domain');

          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${error.response.data?.data?.error || error.message}`,
            'zoho-api'
          );
        } else {
          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${(error as Error).message}`,
            'zoho-api'
          );
        }

        // If we haven't reached max retries, continue to next attempt
        if (retryCount < maxRetries) {
          retryCount++;
          continue;
        }

        // If we've exhausted all retries, throw the last error
        throw lastError;
      }
    }

    // This should never be reached, but just in case
    throw lastError || new ExternalServiceError('Failed to verify domain after multiple attempts', 'zoho-api');
  }

  async createAndVerifySPF(
    hostedZoneId: string,
    domainName: string,
    organization_id: string
  ): Promise<any> {

    logger.info('Verifying SPF with retry mechanism', { hostedZoneId, domainName }, 'zoho-domain/verify-domain');

    const response = await this.route53Service.createSubdomain({
      hostedZoneId: hostedZoneId,
      subdominio: domainName,
      destino: '"v=spf1 include:zohomail.com ~all"',
      tipo: 'TXT',
      ttl: 300
    });

    if (!response) {
      throw new ValidationError('Error creating SPF record');
    }

    let lastError: any = null;
    let retryCount = 0;
    const maxRetries = 20;
    const delayMs = 1000;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          logger.info(`Retry attempt ${retryCount}/${maxRetries} for domain verification`,
            { hostedZoneId, domainName },
            'zoho-domain/verify-domain');
          // Wait before retrying
          await this.delay(delayMs);
        } else {
          logger.info('Verifying domain', { hostedZoneId, domainName }, 'zoho-domain/verify-domain');
        }

        // Get fresh access token
        const accessToken = await this.getAccessToken();

        // Make API request to verify domain
        const response = await axios.put(
          `${this.baseUrl}/organization/${organization_id}/domains/${domainName}`,
          {
            mode: 'VerifySpfRecord',
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Zoho-oauthtoken ${accessToken}`
            }
          }
        );

        // Check if verification was successful
        if (response.data?.status?.code === 200 && response.data?.data?.spfstatus === true) {
          logger.info('Domain verified successfully', { domainName, retryAttempt: retryCount }, 'zoho-domain/verify-domain');
          // Return successful response
          return response.data;
        } else {
          logger.warn('Domain verification failed', {
            domainName,
            retryAttempt: retryCount,
            error: response.data?.data?.error,
            message: response.data?.data?.message
          }, 'zoho-domain/verify-domain');

          // Store the error for potential retry
          lastError = new ExternalServiceError(
            `Domain verification failed: ${response.data?.data?.error || response.data?.data?.message || 'Unknown error'}`,
            'zoho-api'
          );

          // If we haven't reached max retries, continue to next attempt
          if (retryCount < maxRetries) {
            retryCount++;
            continue;
          }

          // Return the response even if verification failed but API call succeeded
          return response.data;
        }
      } catch (error) {
        logger.error('Error verifying domain', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          domainName,
          retryAttempt: retryCount
        }, 'zoho-domain/verify-domain');

        // Store the error for potential retry
        if (axios.isAxiosError(error) && error.response) {
          logger.error('Zoho API error response', {
            status: error.response.status,
            data: error.response.data,
            retryAttempt: retryCount
          }, 'zoho-domain/verify-domain');

          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${error.response.data?.data?.error || error.message}`,
            'zoho-api'
          );
        } else {
          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${(error as Error).message}`,
            'zoho-api'
          );
        }

        // If we haven't reached max retries, continue to next attempt
        if (retryCount < maxRetries) {
          retryCount++;
          continue;
        }

        // If we've exhausted all retries, throw the last error
        throw lastError;
      }
    }

    // This should never be reached, but just in case
    throw lastError || new ExternalServiceError('Failed to verify domain after multiple attempts', 'zoho-api');
  }

  async createAndVerifyMX(
    hostedZoneId: string,
    domainName: string,
    organization_id: string
  ): Promise<any> {

    logger.info('Verifying MX with retry mechanism', { hostedZoneId, domainName }, 'zoho-domain/verify-domain');

    const response = await this.route53Service.createSubdomain({
      hostedZoneId: hostedZoneId,
      subdominio: domainName,
      destino: [
        '10 mx.zoho.com',
        '20 mx2.zoho.com',
        '50 mx3.zoho.com'
      ],
      tipo: 'MX',
      ttl: 300
    });

    if (!response) {
      throw new ValidationError('Error creating MX record');
    }

    let lastError: any = null;
    let retryCount = 0;
    const maxRetries = 20;
    const delayMs = 1000;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          logger.info(`Retry attempt ${retryCount}/${maxRetries} for domain verification`,
            { hostedZoneId, domainName },
            'zoho-domain/verify-domain');
          // Wait before retrying
          await this.delay(delayMs);
        } else {
          logger.info('Verifying domain', { hostedZoneId, domainName }, 'zoho-domain/verify-domain');
        }

        // Get fresh access token
        const accessToken = await this.getAccessToken();

        // Make API request to verify domain
        const response = await axios.put(
          `${this.baseUrl}/organization/${organization_id}/domains/${domainName}`,
          {
            mode: 'verifyMxRecord',
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Zoho-oauthtoken ${accessToken}`
            }
          }
        );

        // Check if verification was successful
        if (response.data?.status?.code === 200 && response.data?.data?.mxstatus === true) {
          logger.info('Domain verified successfully', { domainName, retryAttempt: retryCount }, 'zoho-domain/verify-domain');
          // Return successful response
          return response.data;
        } else {
          logger.warn('Domain verification failed', {
            domainName,
            retryAttempt: retryCount,
            error: response.data?.data?.error,
            message: response.data?.data?.message
          }, 'zoho-domain/verify-domain');

          // Store the error for potential retry
          lastError = new ExternalServiceError(
            `Domain verification failed: ${response.data?.data?.error || response.data?.data?.message || 'Unknown error'}`,
            'zoho-api'
          );

          // If we haven't reached max retries, continue to next attempt
          if (retryCount < maxRetries) {
            retryCount++;
            continue;
          }

          // Return the response even if verification failed but API call succeeded
          return response.data;
        }
      } catch (error) {
        logger.error('Error verifying domain', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          domainName,
          retryAttempt: retryCount
        }, 'zoho-domain/verify-domain');

        // Store the error for potential retry
        if (axios.isAxiosError(error) && error.response) {
          logger.error('Zoho API error response', {
            status: error.response.status,
            data: error.response.data,
            retryAttempt: retryCount
          }, 'zoho-domain/verify-domain');

          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${error.response.data?.data?.error || error.message}`,
            'zoho-api'
          );
        } else {
          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${(error as Error).message}`,
            'zoho-api'
          );
        }

        // If we haven't reached max retries, continue to next attempt
        if (retryCount < maxRetries) {
          retryCount++;
          continue;
        }

        // If we've exhausted all retries, throw the last error
        throw lastError;
      }
    }

    // This should never be reached, but just in case
    throw lastError || new ExternalServiceError('Failed to verify domain after multiple attempts', 'zoho-api');
  }

  /**
   * Fetch all domains in a Zoho organization
   * @returns List of all domains and their details
   */
  async fetchAllDomains(): Promise<any> {
    try {
      logger.info('Fetching all domains', {}, 'zoho-domain/fetch-all-domains');

      // Get fresh access token
      const accessToken = await this.getAccessToken();

      // Make API request to fetch all domains
      const response = await axios.get(
        `${this.baseUrl}/organization/${ZOHO_ORGANIZATION_ID}/domains`,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Zoho-oauthtoken ${accessToken}`
          }
        }
      );

      logger.info('Domains fetched successfully', { count: response.data?.data?.domainVO?.length || 0 }, 'zoho-domain/fetch-all-domains');
      return response.data;
    } catch (error) {
      logger.error('Error fetching all domains', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'zoho-domain/fetch-all-domains');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-domain/fetch-all-domains');

        throw new ExternalServiceError(
          `Error fetching domains from Zoho organization: ${error.response.data?.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error fetching domains from Zoho organization: ${(error as Error).message}`, 'zoho-api');
    }
  }

  /**
   * Fetch a specific domain in a Zoho organization
   * @param domainName Domain name to fetch (e.g., 'example.com')
   * @returns Domain details
   */
  async fetchDomain(domainName: string): Promise<any> {
    try {
      logger.info('Fetching specific domain', { domainName }, 'zoho-domain/fetch-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Get fresh access token
      const accessToken = await this.getAccessToken();

      // Make API request to fetch specific domain
      const response = await axios.get(
        `${this.baseUrl}/organization/${ZOHO_ORGANIZATION_ID}/domains/${domainName}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Zoho-oauthtoken ${accessToken}`
          }
        }
      );

      logger.info('Domain fetched successfully', { domainName }, 'zoho-domain/fetch-domain');
      return response.data;
    } catch (error) {
      logger.error('Error fetching domain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/fetch-domain');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        if (error.response.data.status.code === 500) {
          //Zoho é muito burro e retorna 500 se nao tiver
          return null;
        }

        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-domain/fetch-domain');

        // Handle 404 specifically
        if (error.response.status === 404) {
          throw new NotFoundError(`Domain ${domainName} not found in Zoho organization`);
        }

        throw new ExternalServiceError(
          `Error fetching domain from Zoho organization: ${error.response.data?.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error fetching domain from Zoho organization: ${(error as Error).message}`, 'zoho-api');
    }
  }

  /**
   * Remove CNAME validation record for a domain
   * @param domainName Domain name to remove CNAME validation record for
   * @param hostedZoneId The AWS Route53 hosted zone ID
   * @param CNAMEVerificationCode The CNAME verification code used for validation
   * @returns Result of the removal operation
   */
  async removeCNAMEValidationRecord(domainName: string, hostedZoneId: string, CNAMEVerificationCode?: string): Promise<any> {
    try {
      logger.info('Removing CNAME validation record', { domainName, hostedZoneId }, 'zoho-domain/remove-cname-validation');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      if (!hostedZoneId) {
        throw new ValidationError('Hosted zone ID is required');
      }

      // If CNAME verification code is not provided, fetch domain details to get it
      if (!CNAMEVerificationCode) {
        logger.debug('CNAME verification code not provided, fetching domain details', { domainName }, 'zoho-domain/remove-cname-validation');
        const domainDetails = await this.fetchDomain(domainName);
        CNAMEVerificationCode = domainDetails.data?.CNAMEVerificationCode;

        if (!CNAMEVerificationCode) {
          logger.warn('CNAME verification code not found in domain details', { domainName }, 'zoho-domain/remove-cname-validation');
          return null;
        }
      }

      // Remove the CNAME record using Route53Service
      const result = await this.route53Service.removeSubdomain({
        hostedZoneId: hostedZoneId,
        subdominio: `${CNAMEVerificationCode}.${domainName}`,
        destino: 'zmverify.zoho.com',
        tipo: 'CNAME',
        ttl: 300
      });

      if (result) {
        logger.info('CNAME validation record removed successfully', {
          domainName,
          CNAMEVerificationCode
        }, 'zoho-domain/remove-cname-validation');
      } else {
        logger.warn('Failed to remove CNAME validation record', {
          domainName,
          CNAMEVerificationCode
        }, 'zoho-domain/remove-cname-validation');
      }

      return result;
    } catch (error) {
      logger.error('Error removing CNAME validation record', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName,
        CNAMEVerificationCode
      }, 'zoho-domain/remove-cname-validation');

      // We don't throw here to prevent disrupting the main workflow
      // Just log the error and return null
      return null;
    }
  }

  /**
   * Helper function to delay execution
   * @param ms Milliseconds to delay
   * @returns Promise that resolves after the specified delay
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Verify a domain in a Zoho organization with retry mechanism
   * @param domainName Domain name to verify (e.g., 'example.com')
   * @param verificationMethod Method to use for verification ('TXT', 'CNAME', or 'HTML')
   * @param maxRetries Maximum number of retry attempts (default: 3)
   * @param delayMs Delay between retry attempts in milliseconds (default: 5000)
   * @returns Verification result
   */
  async verifyDomain(
    domainName: string,
    config_id: string,
    verificationMethod: 'TXT' | 'CNAME' | 'HTML' = 'TXT',
    maxRetries: number = 3,
    delayMs: number = 3000
  ): Promise<any> {

    logger.info('Verifying domain with retry mechanism', { domainName, verificationMethod }, 'zoho-domain/verify-domain');

    if (!domainName) {
      throw new ValidationError('Domain name is required');
    }

    const config = await this.jusmailConfigRepository.getByDomainName(domainName);
    if (!config) {
      throw new ValidationError('Config not found for domain');
    }

    let lastError: any = null;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          logger.info(`Retry attempt ${retryCount}/${maxRetries} for domain verification`,
            { domainName, verificationMethod },
            'zoho-domain/verify-domain');
          // Wait before retrying
          await this.delay(delayMs);
        } else {
          logger.info('Verifying domain', { domainName, verificationMethod }, 'zoho-domain/verify-domain');
        }

        if (!domainName) {
          throw new ValidationError('Domain name is required');
        }

        // Map verification method to API mode
        let mode: string;
        switch (verificationMethod) {
          case 'TXT':
            mode = 'verifyDomainByTXT';
            break;
          case 'CNAME':
            mode = 'verifyDomainByCName';
            break;
          case 'HTML':
            mode = 'verifyDomainByHTML';
            break;
          default:
            throw new ValidationError('Invalid verification method. Use TXT, CNAME, or HTML');
        }

        // Get fresh access token
        const accessToken = await this.getAccessToken();

        // Make API request to verify domain
        const response = await axios.put(
          `${this.baseUrl}/organization/${ZOHO_ORGANIZATION_ID}/domains/${domainName}`,
          { mode },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Zoho-oauthtoken ${accessToken}`
            }
          }
        );

        // Check if verification was successful
        if (response.data?.status?.code === 200) {
          logger.info('Domain verified successfully', { domainName, retryAttempt: retryCount }, 'zoho-domain/verify-domain');

          const updatedStatusVerified = this.jusmailConfigRepository.update(config_id, { status: JusMailConfigurationStatus.VERIFIED, verified_at: new Date() });
          if (!updatedStatusVerified) {
            throw new ValidationError('Error updating config');
          }

          // If verification method was CNAME, remove the CNAME validation record
          if (verificationMethod === 'CNAME') {
            logger.info('Removing CNAME validation record after successful verification', { domainName }, 'zoho-domain/verify-domain');
            /* FIXO NO MOMENTO - Same as in addDomainToOrganization */
            const hostedZoneId = 'Z06507317BTHBYU79AGF'; // await this.route53Service.getHostedZoneId(domainName);
            await this.removeCNAMEValidationRecord(domainName, hostedZoneId, undefined);
          }

          // Return successful response
          return response.data;
        } else {
          const updatedStatusVerificationFailed = await this.jusmailConfigRepository.update(config_id, { status: JusMailConfigurationStatus.VERIFICATION_FAILED });
          if (!updatedStatusVerificationFailed) {
            throw new ValidationError('Error updating config');
          }

          logger.warn('Domain verification failed', {
            domainName,
            retryAttempt: retryCount,
            error: response.data?.data?.error,
            message: response.data?.data?.message
          }, 'zoho-domain/verify-domain');

          // Store the error for potential retry
          lastError = new ExternalServiceError(
            `Domain verification failed: ${response.data?.data?.error || response.data?.data?.message || 'Unknown error'}`,
            'zoho-api'
          );

          // If we haven't reached max retries, continue to next attempt
          if (retryCount < maxRetries) {
            retryCount++;
            continue;
          }

          // Return the response even if verification failed but API call succeeded
          return response.data;
        }
      } catch (error) {
        logger.error('Error verifying domain', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          domainName,
          verificationMethod,
          retryAttempt: retryCount
        }, 'zoho-domain/verify-domain');

        // Store the error for potential retry
        if (axios.isAxiosError(error) && error.response) {
          logger.error('Zoho API error response', {
            status: error.response.status,
            data: error.response.data,
            retryAttempt: retryCount
          }, 'zoho-domain/verify-domain');

          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${error.response.data?.data?.error || error.message}`,
            'zoho-api'
          );
        } else {
          lastError = new ExternalServiceError(
            `Error verifying domain in Zoho organization: ${(error as Error).message}`,
            'zoho-api'
          );
        }

        // If we haven't reached max retries, continue to next attempt
        if (retryCount < maxRetries) {
          retryCount++;
          continue;
        }

        // If we've exhausted all retries, throw the last error
        throw lastError;
      }
    }

    // This should never be reached, but just in case
    throw lastError || new ExternalServiceError('Failed to verify domain after multiple attempts', 'zoho-api');
  }

  /**
   * Delete a domain from a Zoho organization
   * @param domainName Domain name to delete (e.g., 'example.com')
   * @returns Deletion result
   */
  async deleteDomain(domainName: string): Promise<any> {
    try {
      logger.info('Deleting domain', { domainName }, 'zoho-domain/delete-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Get fresh access token
      const accessToken = await this.getAccessToken();

      const config = await this.jusmailConfigRepository.getByDomainName(domainName);
      if (!config) {
        throw new ValidationError('Domain not found');
      }

      await this.enableDisableEmailHosting(config.organization_id, domainName, false);

      // Make API request to delete domain
      const response = await axios.delete(
        `${this.baseUrl}/organization/${config.organization_id}/domains/${domainName}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Zoho-oauthtoken ${accessToken}`
          }
        }
      );

      logger.info('Domain deleted successfully', { domainName }, 'zoho-domain/delete-domain');

      const promiseDKIM = this.removeDKIMRecord(config.hosted_zone_id, domainName, config.dkim_public_key);
      const promiseSPF = this.removeSPFRecord(config.hosted_zone_id, domainName);
      const promiseMX = this.removeMXRecord(config.hosted_zone_id, domainName);

      const results = await Promise.all([promiseDKIM, promiseSPF, promiseMX]);

      const deleted = await this.jusmailConfigRepository.deleteByDomainName(domainName);
      if (!deleted) {
        throw new ValidationError('Error deleting config');
      }

      return response.data;
    } catch (error) {
      logger.error('Error deleting domain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/delete-domain');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-domain/delete-domain');

        // Handle 404 specifically
        if (error.response.status === 404) {
          throw new NotFoundError(`Domain ${domainName} not found in Zoho organization`);
        }

        throw new ExternalServiceError(
          `Error deleting domain from Zoho organization: ${error.response.data?.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error deleting domain from Zoho organization: ${(error as Error).message}`, 'zoho-api');
    }
  }

  async removeMXRecord(hostedZoneId: string, domainName: string): Promise<any> {
    try {
      logger.info('Removing MX record', { domainName, hostedZoneId }, 'zoho-domain/remove-mx-record');

      // Remove the MX record using Route53Service
      const result = await this.route53Service.removeSubdomain({
        hostedZoneId: hostedZoneId,
        subdominio: domainName,
        destino: [
          '10 mx.zoho.com',
          '20 mx2.zoho.com',
          '50 mx3.zoho.com'
        ],
        tipo: 'MX',
        ttl: 300
      });

      if (result) {
        logger.info('MX record removed successfully', {
          domainName
        }, 'zoho-domain/remove-mx-record');
      } else {
        logger.warn('Failed to remove MX record', {
          domainName
        }, 'zoho-domain/remove-mx-record');
      }

      return result;
    } catch (error) {
      logger.error('Error removing MX record', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/remove-mx-record');

      // We don't throw here to prevent disrupting the main workflow
      // Just log the error and return null
      return null;
    }
  }

  async removeSPFRecord(hostedZoneId: string, domainName: string): Promise<any> {
    try {
      logger.info('Removing SPF record', { domainName, hostedZoneId }, 'zoho-domain/remove-spf-record');

      // Remove the SPF record using Route53Service
      const result = await this.route53Service.removeSubdomain({
        hostedZoneId: hostedZoneId,
        subdominio: domainName,
        destino: '"v=spf1 include:zohomail.com ~all"',
        tipo: 'TXT',
        ttl: 300
      });

      if (result) {
        logger.info('SPF record removed successfully', {
          domainName
        }, 'zoho-domain/remove-spf-record');
      } else {
        logger.warn('Failed to remove SPF record', {
          domainName
        }, 'zoho-domain/remove-spf-record');
      }

      return result;
    } catch (error) {
      logger.error('Error removing SPF record', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/remove-spf-record');

      // We don't throw here to prevent disrupting the main workflow
      // Just log the error and return null
      return null;
    }
  }

  async removeDKIMRecord(hostedZoneId: string, domainName: string, publicKey: string): Promise<any> {
    try {
      logger.info('Removing DKIM record', { domainName, hostedZoneId }, 'zoho-domain/remove-mx-record');

      // Remove the DKIM record using Route53Service
      const result = await this.route53Service.removeSubdomain({
        hostedZoneId: hostedZoneId,
        subdominio: `zmail._domainkey.${domainName}`,
        destino: `"${publicKey}"`,
        tipo: 'TXT',
        ttl: 300
      });

      if (result) {
        logger.info('DKIM record removed successfully', {
          domainName
        }, 'zoho-domain/remove-mx-record');
      } else {
        logger.warn('Failed to remove DKIM record', {
          domainName
        }, 'zoho-domain/remove-mx-record');
      }

      return result;
    } catch (error) {
      logger.error('Error removing DKIM record', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/remove-mx-record');

      // We don't throw here to prevent disrupting the main workflow
      // Just log the error and return null
      return null;
    }
  }

  async enableDisableEmailHosting(organizationId: string, domainName: string, enable: boolean): Promise<any> {
    try {
      logger.info('Enabling email hosting', { domainName }, 'zoho-domain/enable-email-hosting');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Get fresh access token
      const accessToken = await this.getAccessToken();

      // Make API request to enable email hosting
      const response = await axios.put(
        `${this.baseUrl}/organization/${organizationId}/domains/${domainName}`,
        {
          mode: enable ? 'enableMailHosting' : 'disableMailHosting'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Zoho-oauthtoken ${accessToken}`
          }
        }
      );

      logger.info('Email hosting enabled successfully', { domainName }, 'zoho-domain/enable-email-hosting');

      return response.data;
    } catch (error) {
      logger.error('Error enabling email hosting', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/enable-email-hosting');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-domain/enable-email-hosting');

        throw new ExternalServiceError(
          `Error enabling email hosting in Zoho organization: ${error.response.data?.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error enabling email hosting in Zoho organization: ${(error as Error).message}`, 'zoho-api');
    }
  }

  async addDKIMDetails(organizationId: string, domainName: string): Promise<any> {
    try {
      logger.info('Adding DKIM details', { domainName }, 'zoho-domain/add-dkim-details');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Get fresh access token
      const accessToken = await this.getAccessToken();

      // Make API request to add DKIM details
      const response = await axios.put(
        `${this.baseUrl}/organization/${organizationId}/domains/${domainName}`,
        {
          mode: 'addDkimDetail',
          selector: 'zmail',
          keySize: 1024,
          isDefault: true
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Zoho-oauthtoken ${accessToken}`
          }
        }
      );

      logger.info('DKIM details added successfully', { domainName }, 'zoho-domain/add-dkim-details');
      return response.data;
    } catch (error) {
      logger.error('Error adding DKIM details', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'zoho-domain/add-dkim-details');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-domain/add-dkim-details');

        throw new ExternalServiceError(
          `Error adding DKIM details in Zoho organization: ${error.response.data?.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error adding DKIM details in Zoho organization: ${(error as Error).message}`, 'zoho-api');
    }
  }

}
