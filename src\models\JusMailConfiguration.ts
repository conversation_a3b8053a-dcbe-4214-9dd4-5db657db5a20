import { Model, DataTypes, Sequelize } from 'sequelize';

export enum JusMailConfigurationStatus {
  CREATED = 'CREATED',
  ORDERED = 'ORDERED',
  ACCEPTED = 'ACCEPTED',
  REGISTERED = 'REGISTERED',
  VERIFICATION_PENDING = 'VERIFICATION_PENDING',
  VERIFICATION_FAILED = 'VERIFICATION_FAILED',
  VERIFIED = 'VERIFIED',
}

interface JusMailConfigurationAttributes {
  id: string;
  user_id: number;
  client_id: number;
  organization_id?: string;
  domain_name: string;
  email: string;
  account_id?: string;
  hosted_zone_id?: string;
  status: JusMailConfigurationStatus;
  verification_code?: string;
  verified_at?: Date;
  zoho_domain_data?: any;
  domain_id?: string;
  dkim_id?: string;
  dkim_public_key?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

class JusMailConfiguration extends Model<JusMailConfigurationAttributes> implements JusMailConfigurationAttributes {
  public id!: string;
  public user_id!: number;
  public client_id!: number;
  public organization_id?: string;
  public domain_name!: string;
  public email!: string;
  public account_id?: string;
  public hosted_zone_id?: string;
  public status!: JusMailConfigurationStatus;
  public verification_code?: string;
  public verified_at?: Date;
  public zoho_domain_data?: any;
  public domain_id?: string;
  public dkim_id?: string;
  public dkim_public_key?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations can be defined here if needed
  // public static associate(_models: any): void {
  // }
}

export default (sequelize: Sequelize, _dataTypes: typeof DataTypes): typeof JusMailConfiguration => {
  JusMailConfiguration.init(
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      client_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      organization_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      domain_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      account_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      hosted_zone_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM(...Object.values(JusMailConfigurationStatus)),
        allowNull: false,
        defaultValue: JusMailConfigurationStatus.CREATED
      },
      verification_code: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      verified_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      zoho_domain_data: {
        type: DataTypes.JSON,
        allowNull: true
      },
      domain_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      dkim_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      dkim_public_key: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    },
    {
      sequelize,
      modelName: 'JusMailConfiguration',
      tableName: 'jusmail_configuration',
      timestamps: true
    }
  );

  return JusMailConfiguration;
};
