import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import logger from '../utils/logger';
import { ConfigError, ExternalServiceError, NotFoundError, ValidationError } from '../types/errors';

import { DomainService } from './domain.service';

const JOINVIX_API_URL = process.env.JOINVIX_API_URL || '';
const JOINVIX_API_KEY = process.env.JOINVIX_API_KEY || '';
const JOINVIX_API_SECRET = process.env.JOINVIX_API_SECRET || '';
const JOINVIX_CONTACT_ID = process.env.JOINVIX_CONTACT_ID || '';

// Domain order interface
interface DomainOrderParams {
  domain: string;
  ns1: string;
  ns2: string;
  ns3: string;
  ns4: string;
}

export class JoinvixService {
  private authToken: string | null = null;
  private tokenExpiresAt: Date | null = null;
  private domainService: DomainService;

  constructor() {
    if (!JOINVIX_API_URL) {
      logger.error('JOINVIX API URL is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX API URL is not set');
    }

    if (!JOINVIX_API_KEY) {
      logger.error('JOINVIX API key is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX API key is not set');
    }

    if (!JOINVIX_API_SECRET) {
      logger.error('JOINVIX API secret is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX API secret is not set');
    }

    if (!JOINVIX_CONTACT_ID) {
      logger.error('JOINVIX contact ID is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX contact ID is not set');
    }

    this.domainService = new DomainService();
  }

  /**
   * Get authentication token from JOINVIX API
   * @returns The authentication token
   */
  async getAuthToken(): Promise<string> {
    try {
      logger.debug('Checking if token exists and is valid', {}, 'joinvix-service/get-auth-token');

      if (this.isTokenValid()) {
        logger.debug('Using existing token', {}, 'joinvix-service/get-auth-token');
        return this.authToken as string;
      }

      logger.info('Requesting new auth token from JOINVIX API', {}, 'joinvix-service/get-auth-token');

      const url = `${JOINVIX_API_URL}?service=auth&action=token`;
      const response = await axios.post(url, {
        api_key: JOINVIX_API_KEY,
        api_secret: JOINVIX_API_SECRET
      });

      if (!response.data || !response.data.token) {
        throw new ExternalServiceError('Invalid response from JOINVIX API', 'joinvix-api');
      }

      this.authToken = response.data.token;
      const expiresInSeconds = response.data.expires_in || 3600;
      this.tokenExpiresAt = new Date();
      this.tokenExpiresAt.setSeconds(this.tokenExpiresAt.getSeconds() + expiresInSeconds);

      logger.info('JOINVIX auth token obtained successfully', {
        expiresIn: expiresInSeconds,
        expiresAt: this.tokenExpiresAt
      }, 'joinvix-service/get-auth-token');
      return this.authToken as string;
    } catch (error) {
      logger.error('Error getting JOINVIX auth token', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'joinvix-service/get-auth-token');

      if (axios.isAxiosError(error)) {
        if (error.response) {
          logger.error('JOINVIX API error response', {
            status: error.response.status,
            data: error.response.data
          }, 'joinvix-service/get-auth-token');
        }
        throw new ExternalServiceError(`Error communicating with JOINVIX API: ${error.message}`, 'joinvix-api');
      }

      throw new ExternalServiceError(`Error getting JOINVIX auth token: ${(error as Error).message}`, 'joinvix-api');
    }
  }

  /**
   * Check if the current token is valid
   * @returns True if the token exists and has not expired, false otherwise
   */
  isTokenValid(): boolean {
    return !!(this.authToken && this.tokenExpiresAt && this.tokenExpiresAt > new Date());
  }

  /**
   * Create a domain order in JoinVix
   * @param params Domain order parameters
   * @returns Order creation response
   */
  async createAndAcceptOrder(params: DomainOrderParams): Promise<any> {
    try {
      logger.info('Creating domain order', { domain: params.domain }, 'joinvix-service/create-order');

      const { domain, ns1, ns2, ns3, ns4 } = params;

      // Get auth token
      const token = await this.getAuthToken();

      // Create order
      const urlCreateOrder = `${JOINVIX_API_URL}?service=domains&action=order`;
      const responseCreateOrder = await axios.post(urlCreateOrder, {
        domain: domain,
        billingcycle: 'annually',
        domaintype: 'register',
        regperiod: 1,
        nameserver1: ns1,
        nameserver2: ns2,
        nameserver3: ns3,
        nameserver4: ns4,
        register_number: '40.573.276/0001-83',
        paymentmethod: 'mailin'
      }, {
        headers: {
          'X-Authorization': token
        }
      });
      
      if (responseCreateOrder.data?.status !== 'success') {
        throw new ExternalServiceError('Error creating domain order', 'joinvix-api');
      }

      // Accept order
      const urlAcceptOrder = `${JOINVIX_API_URL}?service=domains&action=accept`;
      const responseAcceptOrder = await axios.post(urlAcceptOrder, {
        order_id: responseCreateOrder.data.data.orderid
      }, {
        headers: {
          'X-Authorization': token
        }
      });

      if (responseAcceptOrder.data?.status !== 'success') {
        throw new ExternalServiceError('Error accepting domain order', 'joinvix-api');
      }

      logger.info('Domain order created successfully', {
        domain: domain,
        response: responseCreateOrder.data
      }, 'joinvix-service/create-order');

      return responseCreateOrder.data;
    } catch (error) {
      logger.error('Error creating domain order', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain: params.domain
      }, 'joinvix-service/create-order');

      if (axios.isAxiosError(error) && error.response) {
        logger.error('JoinVix API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'joinvix-service/create-order');

        throw new ExternalServiceError(
          `Error creating domain order: ${error.response.data?.message || error.message}`,
          'joinvix-api'
        );
      }

      throw new ExternalServiceError(`Error creating domain order: ${(error as Error).message}`, 'joinvix-api');
    }
  }
}

