import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import JusMailConfiguration, { JusMailConfigurationStatus } from '../models/JusMailConfiguration';
import sequelize from '../models/index';
import { DataTypes } from 'sequelize';
import { DatabaseError, NotFoundError } from '../types/errors';

export interface CreateJusMailConfigurationDto {
  user_id: number;
  client_id: number;
  domain_name: string;
  email: string;
  hosted_zone_id: string;
  organization_id?: string;
}

export interface UpdateJusMailConfigurationDto {
  organization_id?: string;
  hosted_zone_id?: string;
  status?: JusMailConfigurationStatus;
  verification_code?: string;
  verified_at?: Date;
  zoho_domain_data?: any;
  domain_id?: string;
  dkim_id?: string;
  dkim_public_key?: string;
}

export class JusMailConfigurationRepository {
  constructor() {}

  /**
   * Create a new JusMail configuration
   * @param createDto Data to create the configuration
   * @returns The created configuration ID
   */
  async create(createDto: CreateJusMailConfigurationDto): Promise<string> {
    logger.debug("Creating JusMail configuration in repository", { domain_name: createDto.domain_name }, 'jusmail-config-repo/create');
    try {
      const jusmailConfig = await JusMailConfiguration(sequelize, DataTypes).create({
        id: uuidv4(),
        user_id: createDto.user_id,
        client_id: createDto.client_id,
        domain_name: createDto.domain_name,
        email: createDto.email,
        organization_id: createDto.organization_id || undefined,
        hosted_zone_id: createDto.hosted_zone_id || undefined,
        status: JusMailConfigurationStatus.CREATED,
      });

      return jusmailConfig.id;
    } catch (error) {
      logger.error("Error creating JusMail configuration", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name: createDto.domain_name
      }, 'jusmail-config-repo/create');
      throw new DatabaseError(`Error creating JusMail configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Get a v configuration by ID
   * @param id Configuration ID
   * @returns The configuration or null if not found
   */
  async getById(id: string): Promise<any> {
    logger.debug("Getting JusMail configuration by ID", { id }, 'jusmail-config-repo/get-by-id');
    try {
      const config = await JusMailConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      return config;
    } catch (error) {
      logger.error("Error getting JusMail configuration by ID", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        id
      }, 'jusmail-config-repo/get-by-id');
      throw new DatabaseError(`Error getting JusMail configuration by ID: ${(error as Error).message}`);
    }
  }

  /**
   * Get a JusMail configuration by domain name
   * @param domain_name Domain name
   * @returns The configuration or null if not found
   */
  async getByDomainName(domain_name: string): Promise<any> {
    logger.debug("Getting JusMail configuration by domain name", { domain_name }, 'jusmail-config-repo/get-by-domain');
    try {
      const config = await JusMailConfiguration(sequelize, DataTypes).findOne({ where: { domain_name } });
      return config;
    } catch (error) {
      logger.error("Error getting JusMail configuration by domain name", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name
      }, 'jusmail-config-repo/get-by-domain');
      throw new DatabaseError(`Error getting JusMail configuration by domain name: ${(error as Error).message}`);
    }
  }

  /**
   * Get all JusMail configurations for a user
   * @param user_id User ID
   * @returns Array of configurations
   */
  async getByUserId(user_id: number): Promise<any[]> {
    logger.debug("Getting JusMail configurations by user ID", { user_id }, 'jusmail-config-repo/get-by-user');
    try {
      const configs = await JusMailConfiguration(sequelize, DataTypes).findAll({ where: { user_id } });
      return configs;
    } catch (error) {
      logger.error("Error getting JusMail configurations by user ID", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        user_id
      }, 'jusmail-config-repo/get-by-user');
      throw new DatabaseError(`Error getting JusMail configurations by user ID: ${(error as Error).message}`);
    }
  }

  /**
   * Get all JusMail configurations for a client
   * @param client_id Client ID
   * @returns Array of configurations
   */
  async getByClientId(client_id: number): Promise<any[]> {
    logger.debug("Getting JusMail configurations by client ID", { client_id }, 'jusmail-config-repo/get-by-client');
    try {
      const configs = await JusMailConfiguration(sequelize, DataTypes).findAll({ where: { client_id } });
      return configs;
    } catch (error) {
      logger.error("Error getting JusMail configurations by client ID", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        client_id
      }, 'jusmail-config-repo/get-by-client');
      throw new DatabaseError(`Error getting JusMail configurations by client ID: ${(error as Error).message}`);
    }
  }

  /**
   * Update a JusMail configuration
   * @param id Configuration ID
   * @param updateDto Data to update
   * @returns Number of rows affected
   */
  async update(id: string, updateDto: UpdateJusMailConfigurationDto): Promise<number> {
    logger.debug("Updating JusMail configuration", { id }, 'jusmail-config-repo/update');
    try {
      const config = await JusMailConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      
      if (!config) {
        throw new NotFoundError(`JusMail configuration with ID ${id} not found`);
      }
      
      const [affectedRows] = await JusMailConfiguration(sequelize, DataTypes).update(updateDto, { where: { id } });
      return affectedRows;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error updating JusMail configuration", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        id
      }, 'jusmail-config-repo/update');
      throw new DatabaseError(`Error updating JusMail configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Update a JusMail configuration by domain name
   * @param domain_name Domain name
   * @param updateDto Data to update
   * @returns Number of rows affected
   */
  async updateByDomainName(domain_name: string, updateDto: UpdateJusMailConfigurationDto): Promise<number> {
    logger.debug("Updating JusMail configuration by domain name", { domain_name }, 'jusmail-config-repo/update-by-domain');
    try {
      const config = await JusMailConfiguration(sequelize, DataTypes).findOne({ where: { domain_name } });
      
      if (!config) {
        throw new NotFoundError(`JusMail configuration with domain name ${domain_name} not found`);
      }
      
      const [affectedRows] = await JusMailConfiguration(sequelize, DataTypes).update(updateDto, { where: { domain_name } });
      return affectedRows;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error updating JusMail configuration by domain name", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name
      }, 'jusmail-config-repo/update-by-domain');
      throw new DatabaseError(`Error updating JusMail configuration by domain name: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a JusMail configuration
   * @param id Configuration ID
   * @returns Boolean indicating success
   */
  async delete(id: string): Promise<boolean> {
    logger.debug("Deleting JusMail configuration", { id }, 'jusmail-config-repo/delete');
    try {
      const config = await JusMailConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      
      if (!config) {
        throw new NotFoundError(`JusMail configuration with ID ${id} not found`);
      }
      
      await JusMailConfiguration(sequelize, DataTypes).destroy({ where: { id } });
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error deleting JusMail configuration", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        id
      }, 'jusmail-config-repo/delete');
      throw new DatabaseError(`Error deleting JusMail configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a JusMail configuration by domain name
   * @param domain_name Domain name
   * @returns Boolean indicating success
   */
  async deleteByDomainName(domain_name: string): Promise<boolean> {
    logger.debug("Deleting JusMail configuration by domain name", { domain_name }, 'jusmail-config-repo/delete-by-domain');
    try {
      const config = await JusMailConfiguration(sequelize, DataTypes).findOne({ where: { domain_name } });
      
      if (!config) {
        throw new NotFoundError(`JusMail configuration with domain name ${domain_name} not found`);
      }
      
      await JusMailConfiguration(sequelize, DataTypes).destroy({ where: { domain_name } });
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error deleting JusMail configuration by domain name", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name
      }, 'jusmail-config-repo/delete-by-domain');
      throw new DatabaseError(`Error deleting JusMail configuration by domain name: ${(error as Error).message}`);
    }
  }
}
